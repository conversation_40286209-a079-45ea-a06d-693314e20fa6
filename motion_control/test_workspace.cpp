#include "kinematics/Classic6dofKine.h"
#include <iostream>
#include <fstream>
#include <iomanip>

int main() {
    std::cout << "=== 机械臂工作空间计算测试 ===" << std::endl;
    
    // 定义关节限位（与Robot.cpp中的定义保持一致）
    std::array<float, 6> joint_limits_max = {
        M_PI * 170.0f / 180.0f,  // Joint 1: ±170°
        M_PI_2,                   // Joint 2: ±90°
        M_PI * 60.0f / 180.0f,   // Joint 3: +60°/-180°
        M_PI * 170.0f / 180.0f,  // Joint 4: ±170°
        M_PI * 120.0f / 180.0f,  // Joint 5: ±120°
        M_PI * 2.0f              // Joint 6: ±360°
    };
    
    std::array<float, 6> joint_limits_min = {
        -M_PI * 170.0f / 180.0f,
        -M_PI_2,
        -M_PI,
        -M_PI * 170.0f / 180.0f,
        -M_PI * 120.0f / 180.0f,
        -M_PI * 2.0f
    };
    
    std::cout << "关节限位设置:" << std::endl;
    for (int i = 0; i < 6; i++) {
        std::cout << "关节" << (i+1) << ": [" 
                  << std::fixed << std::setprecision(1)
                  << joint_limits_min[i] * 180.0f / M_PI << "°, "
                  << joint_limits_max[i] * 180.0f / M_PI << "°]" << std::endl;
    }
    
    // 1. 计算工作空间边界
    std::cout << "\n=== 计算工作空间边界 ===" << std::endl;
    std::array<float, 3> min_bounds, max_bounds;
    calculateWorkspaceBounds(min_bounds, max_bounds, joint_limits_max, joint_limits_min);
    
    std::cout << "工作空间边界:" << std::endl;
    std::cout << "X: [" << std::fixed << std::setprecision(1) 
              << min_bounds[0] << ", " << max_bounds[0] << "] mm" << std::endl;
    std::cout << "Y: [" << min_bounds[1] << ", " << max_bounds[1] << "] mm" << std::endl;
    std::cout << "Z: [" << min_bounds[2] << ", " << max_bounds[2] << "] mm" << std::endl;
    
    // 2. 生成工作空间点云
    std::cout << "\n=== 生成工作空间点云 ===" << std::endl;
    std::vector<std::array<float, 3>> workspace_points;
    generateWorkspacePointCloud(workspace_points, joint_limits_max, joint_limits_min, 36);
    
    std::cout << "生成的工作空间点数: " << workspace_points.size() << std::endl;
    
    // 3. 保存点云到文件
    std::cout << "\n=== 保存点云数据 ===" << std::endl;
    std::ofstream file("workspace_points.txt");
    if (file.is_open()) {
        file << "# 机械臂工作空间点云数据\n";
        file << "# 格式: X Y Z (单位: mm)\n";
        file << "# 总点数: " << workspace_points.size() << "\n";
        
        for (const auto& point : workspace_points) {
            file << std::fixed << std::setprecision(3)
                 << point[0] << " " << point[1] << " " << point[2] << "\n";
        }
        file.close();
        std::cout << "点云数据已保存到 workspace_points.txt" << std::endl;
    } else {
        std::cout << "无法创建输出文件" << std::endl;
    }
    
    // 4. 测试几个典型位置是否在工作空间内
    std::cout << "\n=== 测试位置是否在工作空间内 ===" << std::endl;
    
    std::vector<std::array<float, 6>> test_poses = {
        {0.0f, 0.0f, 500.0f, 0.0f, 0.0f, 0.0f},      // 机械臂正上方
        {400.0f, 0.0f, 200.0f, 0.0f, 0.0f, 0.0f},    // 前方
        {0.0f, 400.0f, 200.0f, 0.0f, 0.0f, 0.0f},    // 侧方
        {800.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f},      // 远距离
        {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f},        // 原点
        {100.0f, 100.0f, 100.0f, 0.0f, 0.0f, 0.0f}   // 近距离
    };
    
    for (size_t i = 0; i < test_poses.size(); i++) {
        bool in_workspace = isPoseInWorkspace(test_poses[i]);
        std::cout << "位置 " << (i+1) << " [" 
                  << std::fixed << std::setprecision(1)
                  << test_poses[i][0] << ", " 
                  << test_poses[i][1] << ", " 
                  << test_poses[i][2] << "]: "
                  << (in_workspace ? "在工作空间内" : "不在工作空间内") << std::endl;
    }
    
    // 5. 显示DH参数信息
    std::cout << "\n=== DH参数信息 ===" << std::endl;
    std::cout << "L_BS (基座到肩部): 100.0 mm" << std::endl;
    std::cout << "D_BS (基座偏移): 50.0 mm" << std::endl;
    std::cout << "L_SE (肩部到肘部): 340.0 mm" << std::endl;
    std::cout << "L_EW (肘部到腕部): 360.0 mm" << std::endl;
    std::cout << "D_EW (腕部偏移): 30.0 mm" << std::endl;
    std::cout << "L_WT (腕部到工具): 100.0 mm" << std::endl;
    
    std::cout << "\n=== 工作空间计算完成 ===" << std::endl;
    
    return 0;
}
